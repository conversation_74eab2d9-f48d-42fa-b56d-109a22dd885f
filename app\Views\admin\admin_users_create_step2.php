<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users/create') ?>" class="btn btn-secondary">
    ← Back to Step 1
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Create New User
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Step 2 of 2: Roles & Permissions
        </p>
    </div>
</div>

<!-- Progress Indicator -->
<div style="margin-bottom: var(--spacing-xl);">
    <div style="display: flex; align-items: center; gap: var(--spacing-md);">
        <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
            <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--brand-secondary); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">
                ✓
            </div>
            <span style="color: var(--brand-secondary); font-weight: 600;">Account Details</span>
        </div>
        
        <div style="flex: 1; height: 2px; background: var(--brand-primary); margin: 0 var(--spacing-md);"></div>
        
        <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
            <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--brand-primary); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">
                2
            </div>
            <span style="color: var(--brand-primary); font-weight: 600;">Roles & Permissions</span>
        </div>
    </div>
</div>

<!-- Account Summary -->
<div class="card mb-xl">
    <div class="card-header">
        Account Summary
    </div>
    <div style="padding: var(--spacing-lg);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg);">
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                    Username
                </h4>
                <p style="color: var(--text-secondary); margin: 0;">
                    <?= esc($step1_data['username']) ?>
                </p>
            </div>
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                    Email
                </h4>
                <p style="color: var(--text-secondary); margin: 0;">
                    <?= esc($step1_data['email']) ?>
                </p>
            </div>
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                    Full Name
                </h4>
                <p style="color: var(--text-secondary); margin: 0;">
                    <?= esc($step1_data['name']) ?>
                </p>
            </div>
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                    Temporary Password
                </h4>
                <p style="color: var(--brand-primary); margin: 0; font-weight: 600;">
                    <?= esc($temp_password) ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Roles & Permissions Form -->
<div class="card">
    <div class="card-header">
        Roles & Permissions
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <form action="<?= base_url('admin/users/create/step2/temp') ?>" method="post" class="user-roles-form">
            
            <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
            
            <!-- Organization Assignment -->
            <div class="form-group">
                <label for="organization_id" class="form-label">Organization Assignment *</label>
                <select id="organization_id" name="organization_id" class="form-input" required>
                    <option value="">Select Organization</option>
                    <?php foreach ($organizations as $org): ?>
                        <option value="<?= $org['id'] ?>" <?= (old('organization_id') == $org['id']) ? 'selected' : '' ?>>
                            <?= esc($org['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    The organization this user will belong to
                </small>
            </div>

            <!-- Role Selection -->
            <div class="form-group">
                <label class="form-label">User Role *</label>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-md); margin-top: var(--spacing-sm);">
                    
                    <!-- Admin Role -->
                    <div class="role-option">
                        <input type="radio" id="role_admin" name="role" value="admin" <?= (old('role') === 'admin') ? 'checked' : '' ?> required>
                        <label for="role_admin" class="role-card">
                            <div class="role-header">
                                <span class="role-icon" style="background: var(--brand-danger);">👑</span>
                                <span class="role-title">Administrator</span>
                            </div>
                            <p class="role-description">
                                Full system access with all permissions. Can manage users, projects, and system settings.
                            </p>
                        </label>
                    </div>

                    <!-- Moderator Role -->
                    <div class="role-option">
                        <input type="radio" id="role_moderator" name="role" value="moderator" <?= (old('role') === 'moderator') ? 'checked' : '' ?>>
                        <label for="role_moderator" class="role-card">
                            <div class="role-header">
                                <span class="role-icon" style="background: var(--brand-warning);">⚖️</span>
                                <span class="role-title">Moderator</span>
                            </div>
                            <p class="role-description">
                                Can manage projects and moderate content. Limited user management capabilities.
                            </p>
                        </label>
                    </div>

                    <!-- Editor Role -->
                    <div class="role-option">
                        <input type="radio" id="role_editor" name="role" value="editor" <?= (old('role') === 'editor') ? 'checked' : '' ?>>
                        <label for="role_editor" class="role-card">
                            <div class="role-header">
                                <span class="role-icon" style="background: var(--brand-primary);">✏️</span>
                                <span class="role-title">Editor</span>
                            </div>
                            <p class="role-description">
                                Can create and edit projects, documents, and reports. No user management access.
                            </p>
                        </label>
                    </div>

                    <!-- User Role -->
                    <div class="role-option">
                        <input type="radio" id="role_user" name="role" value="user" <?= (old('role') === 'user') ? 'checked' : '' ?>>
                        <label for="role_user" class="role-card">
                            <div class="role-header">
                                <span class="role-icon" style="background: var(--text-muted);">👤</span>
                                <span class="role-title">User</span>
                            </div>
                            <p class="role-description">
                                Basic access to view projects and submit reports. Limited editing capabilities.
                            </p>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Information Box -->
            <div style="background: var(--bg-accent); border: 1px solid var(--brand-secondary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin: var(--spacing-lg) 0;">
                <h4 style="color: var(--brand-secondary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    ✅ Ready to Create User
                </h4>
                <ul style="color: var(--text-secondary); font-size: 0.875rem; margin: 0; padding-left: var(--spacing-lg);">
                    <li>User will be activated immediately</li>
                    <li>Temporary password: <strong><?= esc($temp_password) ?></strong></li>
                    <li>User should change password on first login</li>
                    <li>You can modify roles and permissions later</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center" style="margin-top: var(--spacing-xl);">
                <a href="<?= base_url('admin/users/create') ?>" class="btn btn-secondary">
                    ← Back to Step 1
                </a>
                
                <button type="submit" class="btn btn-primary">
                    Create User Account
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Role Selection Styling -->
<style>
.role-option {
    position: relative;
}

.role-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.role-card {
    display: block;
    padding: var(--spacing-lg);
    border: 2px solid #E5E7EB;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--surface-card);
}

.role-card:hover {
    border-color: var(--brand-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.role-option input[type="radio"]:checked + .role-card {
    border-color: var(--brand-primary);
    background: var(--bg-accent);
    box-shadow: var(--shadow-lg);
}

.role-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.role-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    color: white;
}

.role-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.role-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .user-roles-form div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .d-flex.justify-content-between .btn {
        width: 100%;
        text-align: center;
    }
}
</style>

<?= $this->endSection() ?>
