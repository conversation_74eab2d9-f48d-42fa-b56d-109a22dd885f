<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrganizationModel;

/**
 * Test Controller for creating admin test users
 * This is for development/testing purposes only
 */
class TestAdminController extends BaseController
{
    public function createTestUser()
    {
        $userModel = new UserModel();
        $organizationModel = new OrganizationModel();

        $output = "<h2>PROMIS User Setup Test</h2>";

        // Check if there are any organizations
        $organizations = $organizationModel->findAll();
        $output .= "<p>Organizations found: " . count($organizations) . "</p>";

        if (empty($organizations)) {
            $output .= "<p>Creating test organization...</p>";

            $orgData = [
                'org_code' => 'TEST1',
                'name' => 'Test Organization',
                'description' => 'Test organization for PROMIS development',
                'license_status' => 'paid',
                'is_active' => 1,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+**********',
                'address_line1' => '123 Test Street',
                'city' => 'Test City',
                'country' => 'Test Country',
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1
            ];

            $orgId = $organizationModel->insert($orgData);
            if ($orgId) {
                $output .= "<p>✅ Test organization created with ID: $orgId</p>";
            } else {
                $output .= "<p>❌ Failed to create test organization</p>";
                $output .= "<pre>" . print_r($organizationModel->errors(), true) . "</pre>";
                return $output;
            }
        } else {
            $orgId = $organizations[0]['id'];
            $output .= "<p>✅ Using existing organization: " . $organizations[0]['name'] . " (ID: $orgId)</p>";
        }

        // Check if there are any users
        $users = $userModel->findAll();
        $output .= "<p>Users found: " . count($users) . "</p>";

        if (empty($users)) {
            $output .= "<p>Creating test admin user...</p>";

            $userData = [
                'organization_id' => $orgId,
                'user_code' => 'ADM001',
                'username' => 'admin',
                'email' => '<EMAIL>',
                'name' => 'Test Administrator',
                'role' => 'admin',
                'password_hash' => password_hash('1234', PASSWORD_ARGON2ID),
                'is_activated' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1
            ];

            $userId = $userModel->insert($userData);
            if ($userId) {
                $output .= "<p>✅ Test admin user created with ID: $userId</p>";
                $output .= "<p>Username: admin<br>Password: 1234<br>Email: <EMAIL></p>";
            } else {
                $output .= "<p>❌ Failed to create test user</p>";
                $output .= "<pre>" . print_r($userModel->errors(), true) . "</pre>";
                return $output;
            }

            // Create a test moderator user
            $output .= "<p>Creating test moderator user...</p>";

            $userData2 = [
                'organization_id' => $orgId,
                'user_code' => 'MOD001',
                'username' => 'moderator',
                'email' => '<EMAIL>',
                'name' => 'Test Moderator',
                'role' => 'moderator',
                'password_hash' => password_hash('1234', PASSWORD_ARGON2ID),
                'is_activated' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1
            ];

            $userId2 = $userModel->insert($userData2);
            if ($userId2) {
                $output .= "<p>✅ Test moderator user created with ID: $userId2</p>";
                $output .= "<p>Username: moderator<br>Password: 1234<br>Email: <EMAIL></p>";
            }

            // Create a test editor user
            $output .= "<p>Creating test editor user...</p>";

            $userData3 = [
                'organization_id' => $orgId,
                'user_code' => 'EDT001',
                'username' => 'editor',
                'email' => '<EMAIL>',
                'name' => 'Test Editor',
                'role' => 'editor',
                'password_hash' => password_hash('1234', PASSWORD_ARGON2ID),
                'is_activated' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => 1
            ];

            $userId3 = $userModel->insert($userData3);
            if ($userId3) {
                $output .= "<p>✅ Test editor user created with ID: $userId3</p>";
                $output .= "<p>Username: editor<br>Password: 1234<br>Email: <EMAIL></p>";
            }

        } else {
            $output .= "<p>✅ Users already exist:</p><ul>";
            foreach ($users as $user) {
                $output .= "<li>{$user['username']} ({$user['role']}) - {$user['email']}</li>";
            }
            $output .= "</ul>";
        }

        $output .= "<h3>Setup Complete</h3>";
        $output .= "<p>You can now test login at: <a href='" . base_url('auth/login') . "'>Login Page</a></p>";
        $output .= "<p><strong>Test credentials:</strong><br>";
        $output .= "Admin: admin / 1234<br>";
        $output .= "Moderator: moderator / 1234<br>";
        $output .= "Editor: editor / 1234</p>";

        return $output;
    }
}
