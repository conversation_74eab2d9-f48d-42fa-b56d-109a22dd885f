<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UserModel;

/**
 * Test Controller for creating admin test users
 * This is for development/testing purposes only
 */
class TestAdminController extends BaseController
{
    public function createTestUser()
    {
        $userModel = new UserModel();
        
        // Check if test user already exists
        $existingUser = $userModel->where('username', 'testadmin')->first();
        
        if ($existingUser) {
            return "Test user 'testadmin' already exists. Password: 1234";
        }
        
        // Create test user data
        $userData = [
            'organization_id' => 2, // Using existing organization ID
            'username' => 'testadmin',
            'email' => '<EMAIL>',
            'name' => 'Test Admin User',
            'role' => 'admin',
            'password_hash' => password_hash('1234', PASSWORD_ARGON2ID),
            'is_activated' => 1,
            'created_by' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            $result = $userModel->insert($userData);
            
            if ($result) {
                return "Test user created successfully!<br>
                        Username: testadmin<br>
                        Password: 1234<br>
                        <a href='" . base_url('admin/login') . "'>Go to Admin Login</a>";
            } else {
                return "Failed to create test user: " . implode(', ', $userModel->errors());
            }
        } catch (\Exception $e) {
            return "Error creating test user: " . $e->getMessage();
        }
    }
}
