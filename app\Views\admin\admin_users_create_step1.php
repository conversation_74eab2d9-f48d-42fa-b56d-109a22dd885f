<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
    ← Back to Users
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Create New User
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Step 1 of 2: Account Details
        </p>
    </div>
</div>

<!-- Progress Indicator -->
<div style="margin-bottom: var(--spacing-xl);">
    <div style="display: flex; align-items: center; gap: var(--spacing-md);">
        <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
            <div style="width: 32px; height: 32px; border-radius: 50%; background: var(--brand-primary); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">
                1
            </div>
            <span style="color: var(--brand-primary); font-weight: 600;">Account Details</span>
        </div>
        
        <div style="flex: 1; height: 2px; background: #E5E7EB; margin: 0 var(--spacing-md);"></div>
        
        <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
            <div style="width: 32px; height: 32px; border-radius: 50%; background: #E5E7EB; color: var(--text-muted); display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">
                2
            </div>
            <span style="color: var(--text-muted);">Roles & Permissions</span>
        </div>
    </div>
</div>

<!-- Form Card -->
<div class="card">
    <div class="card-header">
        Account Information
    </div>
    
    <div style="padding: var(--spacing-xl);">
        <form action="<?= base_url('admin/users/create') ?>" method="post" class="user-create-form">
            
            <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                
                <!-- Username -->
                <div class="form-group">
                    <label for="username" class="form-label">Username *</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        value="<?= old('username') ?>"
                        placeholder="Enter username"
                        required
                        autofocus
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Must be unique and at least 3 characters
                    </small>
                </div>

                <!-- Email -->
                <div class="form-group">
                    <label for="email" class="form-label">Email Address *</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        value="<?= old('email') ?>"
                        placeholder="Enter email address"
                        required
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Must be a valid email address
                    </small>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-lg); margin-bottom: var(--spacing-lg);">
                
                <!-- Full Name -->
                <div class="form-group">
                    <label for="name" class="form-label">Full Name *</label>
                    <input 
                        type="text" 
                        id="name" 
                        name="name" 
                        class="form-input" 
                        value="<?= old('name') ?>"
                        placeholder="Enter full name"
                        required
                    >
                </div>

                <!-- Phone -->
                <div class="form-group">
                    <label for="phone" class="form-label">Phone Number</label>
                    <input 
                        type="tel" 
                        id="phone" 
                        name="phone" 
                        class="form-input" 
                        value="<?= old('phone') ?>"
                        placeholder="Enter phone number"
                    >
                    <small style="color: var(--text-muted); font-size: 0.75rem;">
                        Optional - for contact purposes
                    </small>
                </div>
            </div>

            <!-- Department -->
            <div class="form-group">
                <label for="department" class="form-label">Department</label>
                <input 
                    type="text" 
                    id="department" 
                    name="department" 
                    class="form-input" 
                    value="<?= old('department') ?>"
                    placeholder="Enter department or division"
                >
                <small style="color: var(--text-muted); font-size: 0.75rem;">
                    Optional - user's department or division
                </small>
            </div>

            <!-- Information Box -->
            <div style="background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-lg); margin: var(--spacing-lg) 0;">
                <h4 style="color: var(--brand-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    📋 What happens next?
                </h4>
                <ul style="color: var(--text-secondary); font-size: 0.875rem; margin: 0; padding-left: var(--spacing-lg);">
                    <li>A temporary password will be generated automatically</li>
                    <li>You'll configure roles and organization assignment in step 2</li>
                    <li>The user will be activated immediately upon creation</li>
                    <li>You can share the login credentials with the user</li>
                </ul>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center" style="margin-top: var(--spacing-xl);">
                <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                    Cancel
                </a>
                
                <button type="submit" class="btn btn-primary">
                    Continue to Step 2 →
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Form Validation Styling -->
<style>
.user-create-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-create-form .form-input:invalid {
    border-color: var(--brand-danger);
}

.user-create-form .form-input:valid {
    border-color: var(--brand-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .user-create-form div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
    }
    
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .d-flex.justify-content-between .btn {
        width: 100%;
        text-align: center;
    }
}
</style>

<?= $this->endSection() ?>
