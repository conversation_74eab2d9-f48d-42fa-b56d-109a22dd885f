<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
    ← Back to Users
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Active User Sessions
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Monitor and manage active user sessions
        </p>
    </div>
</div>

<!-- Session Statistics -->
<div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-2xl);">
    
    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.5rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= count($sessions) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Active Sessions
                </p>
            </div>
            <div style="width: 50px; height: 50px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                🔐
            </div>
        </div>
    </div>

    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 1.5rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= count(array_unique(array_column($sessions, 'user_id'))) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Unique Users
                </p>
            </div>
            <div style="width: 50px; height: 50px; background: var(--gradient-secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                👥
            </div>
        </div>
    </div>

    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--brand-secondary); font-size: 1rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    Healthy
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    System Status
                </p>
            </div>
            <div style="width: 50px; height: 50px; background: var(--gradient-secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                ✅
            </div>
        </div>
    </div>
</div>

<!-- Sessions Table -->
<div class="card">
    <div class="card-header">
        Active Sessions
    </div>
    
    <?php if (!empty($sessions)): ?>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Session Info</th>
                        <th>Location</th>
                        <th>Login Time</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sessions as $session): ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                        <?= strtoupper(substr($session['username'], 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                            <?= esc($session['username']) ?>
                                        </div>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                                            ID: <?= esc($session['user_id']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div style="font-size: 0.875rem; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                        Session: <?= esc(substr($session['session_id'], 0, 8)) ?>...
                                    </div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted);">
                                        <?= esc(substr($session['user_agent'], 0, 50)) ?>...
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    <?= esc($session['ip_address']) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?php
                                    // Simple location detection based on IP
                                    $location = 'Unknown';
                                    if ($session['ip_address'] === '::1' || $session['ip_address'] === '127.0.0.1') {
                                        $location = 'Local Development';
                                    }
                                    echo $location;
                                    ?>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                    <?= date('M j, Y', strtotime($session['login_time'])) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: var(--text-muted);">
                                    <?= date('g:i A', strtotime($session['login_time'])) ?>
                                </div>
                            </td>
                            <td>
                                <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?= esc($session['status']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($session['session_id'] !== session()->session_id): ?>
                                    <form method="post" action="<?= base_url('admin/users/sessions/' . $session['session_id'] . '/terminate') ?>" style="display: inline;">
                                        <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
                                        <button 
                                            type="submit" 
                                            class="btn btn-danger"
                                            style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                            onclick="return confirm('Are you sure you want to terminate this session?')"
                                            title="Terminate Session"
                                        >
                                            🚫 Terminate
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <span style="color: var(--text-muted); font-size: 0.75rem; font-style: italic;">
                                        Current Session
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
            <div style="font-size: 3rem; margin-bottom: var(--spacing-md);">🔐</div>
            <p style="font-size: 1.125rem; margin-bottom: var(--spacing-md);">No active sessions found</p>
            <p>All users are currently logged out.</p>
        </div>
    <?php endif; ?>
</div>

<!-- Session Management Information -->
<div class="card mt-xl">
    <div class="card-header">
        Session Management Information
    </div>
    <div style="padding: var(--spacing-lg);">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-lg);">
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    🕒 Session Timeout
                </h4>
                <p style="color: var(--text-secondary); font-size: 0.875rem; margin: 0;">
                    Sessions automatically expire after 30 minutes of inactivity
                </p>
            </div>
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    🔄 Auto Refresh
                </h4>
                <p style="color: var(--text-secondary); font-size: 0.875rem; margin: 0;">
                    This page refreshes automatically every 30 seconds
                </p>
            </div>
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    🛡️ Security
                </h4>
                <p style="color: var(--text-secondary); font-size: 0.875rem; margin: 0;">
                    All sessions are tracked and logged for security purposes
                </p>
            </div>
            <div>
                <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-sm);">
                    📊 Monitoring
                </h4>
                <p style="color: var(--text-secondary); font-size: 0.875rem; margin: 0;">
                    Session data is available in the audit trail
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Auto-refresh script -->
<script>
// Auto-refresh the page every 30 seconds
setTimeout(function() {
    window.location.reload();
}, 30000);

// Show countdown timer
let countdown = 30;
const timer = setInterval(function() {
    countdown--;
    if (countdown <= 0) {
        clearInterval(timer);
    }
}, 1000);
</script>

<?= $this->endSection() ?>
