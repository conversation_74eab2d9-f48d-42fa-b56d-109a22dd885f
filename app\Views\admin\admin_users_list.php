<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/users/create') ?>" class="btn btn-primary">
    👤 Create New User
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            User Management
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Manage user accounts for <strong><?= esc($admin_organization_name) ?></strong>
        </p>
    </div>
</div>

<!-- Filters -->
<div class="card mb-xl">
    <div class="card-header">
        Filters & Search
    </div>
    <div style="padding: var(--spacing-lg);">
        <form method="get" action="<?= base_url('admin/users') ?>" class="d-flex gap-md align-items-center" style="flex-wrap: wrap;">
            
            <!-- Search -->
            <div style="flex: 1; min-width: 200px;">
                <input 
                    type="text" 
                    name="search" 
                    class="form-input" 
                    placeholder="Search users..." 
                    value="<?= esc($filters['search']) ?>"
                >
            </div>

            <!-- Role Filter -->
            <div style="min-width: 150px;">
                <select name="role" class="form-input">
                    <option value="">All Roles</option>
                    <option value="admin" <?= ($filters['role'] === 'admin') ? 'selected' : '' ?>>Admin</option>
                    <option value="moderator" <?= ($filters['role'] === 'moderator') ? 'selected' : '' ?>>Moderator</option>
                    <option value="editor" <?= ($filters['role'] === 'editor') ? 'selected' : '' ?>>Editor</option>
                    <option value="user" <?= ($filters['role'] === 'user') ? 'selected' : '' ?>>User</option>
                </select>
            </div>

            <!-- Status Filter -->
            <div style="min-width: 150px;">
                <select name="status" class="form-input">
                    <option value="">All Status</option>
                    <option value="active" <?= ($filters['status'] === 'active') ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= ($filters['status'] === 'inactive') ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>

            <!-- Organization Context (Auto-filtered) -->
            <div style="min-width: 200px; display: flex; align-items: center; gap: var(--spacing-sm); background: var(--bg-accent); border: 1px solid var(--brand-primary); border-radius: var(--radius-md); padding: var(--spacing-sm) var(--spacing-md);">
                <div style="width: 30px; height: 30px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; font-size: 0.875rem;">
                    🏢
                </div>
                <div>
                    <div style="font-weight: 600; color: var(--brand-primary); font-size: 0.875rem;">
                        <?= esc($admin_organization_name) ?>
                    </div>
                    <div style="color: var(--text-muted); font-size: 0.75rem;">
                        Your Organization
                    </div>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="d-flex gap-md">
                <button type="submit" class="btn btn-primary">Filter</button>
                <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">Clear</a>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        Users (<?= count($users) ?> found)
    </div>
    
    <?php if (!empty($users)): ?>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--gradient-primary); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem;">
                                        <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div style="font-weight: 600; color: var(--text-primary); margin-bottom: var(--spacing-xs);">
                                            <?= esc($user['name']) ?>
                                        </div>
                                        <div style="font-size: 0.75rem; color: var(--text-muted);">
                                            @<?= esc($user['username']) ?> • <?= esc($user['email']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <?php
                                $roleColors = [
                                    'admin' => 'var(--brand-danger)',
                                    'moderator' => 'var(--brand-warning)',
                                    'editor' => 'var(--brand-primary)',
                                    'user' => 'var(--text-muted)'
                                ];
                                $roleColor = $roleColors[$user['role']] ?? 'var(--text-muted)';
                                ?>
                                <span style="background: <?= $roleColor ?>; color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600; text-transform: uppercase;">
                                    <?= esc($user['role']) ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($user['is_activated']): ?>
                                    <span style="background: var(--brand-secondary); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        Active
                                    </span>
                                <?php else: ?>
                                    <span style="background: var(--text-muted); color: white; padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem; font-weight: 600;">
                                        Inactive
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span style="color: var(--text-secondary); font-size: 0.875rem;">
                                    <?= date('M j, Y', strtotime($user['created_at'])) ?>
                                </span>
                            </td>
                            <td>
                                <div class="d-flex gap-md">
                                    <button 
                                        onclick="showResetPasswordModal(<?= $user['id'] ?>, '<?= esc($user['name']) ?>')"
                                        class="btn btn-secondary"
                                        style="padding: var(--spacing-xs) var(--spacing-sm); font-size: 0.75rem;"
                                        title="Reset Password"
                                    >
                                        🔑 Reset
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
            <p style="font-size: 1.125rem; margin-bottom: var(--spacing-md);">No users found</p>
            <p>Try adjusting your filters or create a new user.</p>
            <a href="<?= base_url('admin/users/create') ?>" class="btn btn-primary" style="margin-top: var(--spacing-md);">
                Create First User
            </a>
        </div>
    <?php endif; ?>
</div>

<!-- Reset Password Modal -->
<div id="resetPasswordModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; border-radius: var(--radius-lg); padding: var(--spacing-xl); max-width: 400px; width: 90%;">
        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-lg);">Reset Password</h3>
        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
            Are you sure you want to reset the password for <strong id="resetUserName"></strong>?
        </p>
        <p style="color: var(--text-muted); font-size: 0.875rem; margin-bottom: var(--spacing-lg);">
            A new temporary password will be generated and displayed.
        </p>
        
        <form id="resetPasswordForm" method="post" style="display: none;">
            <?= csrf_field() ?>
        </form>
        
        <div class="d-flex gap-md justify-content-between">
            <button onclick="hideResetPasswordModal()" class="btn btn-secondary">Cancel</button>
            <button onclick="confirmResetPassword()" class="btn btn-danger">Reset Password</button>
        </div>
    </div>
</div>

<script>
let currentResetUserId = null;

function showResetPasswordModal(userId, userName) {
    currentResetUserId = userId;
    document.getElementById('resetUserName').textContent = userName;
    document.getElementById('resetPasswordModal').style.display = 'flex';
}

function hideResetPasswordModal() {
    document.getElementById('resetPasswordModal').style.display = 'none';
    currentResetUserId = null;
}

function confirmResetPassword() {
    if (currentResetUserId) {
        const form = document.getElementById('resetPasswordForm');
        form.action = '<?= base_url('admin/users/') ?>' + currentResetUserId + '/reset-password';
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('resetPasswordModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideResetPasswordModal();
    }
});
</script>

<?= $this->endSection() ?>
