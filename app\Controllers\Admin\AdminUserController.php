<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrganizationModel;

/**
 * Admin User Controller
 * 
 * Handles user management for the PROMIS Admin Portal including:
 * - Two-step user creation process
 * - User listing and management
 * - Password reset functionality
 * - Session management
 */
class AdminUserController extends BaseController
{
    protected $userModel;
    protected $organizationModel;
    
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->organizationModel = new OrganizationModel();
    }

    /**
     * List all users - GET request
     */
    public function listUsers()
    {
        // Get filters from query parameters
        $filters = [
            'role' => $this->request->getGet('role'),
            'status' => $this->request->getGet('status'),
            'organization' => $this->request->getGet('organization'),
            'search' => $this->request->getGet('search')
        ];

        // Get users with organization details
        $users = $this->getUsersWithFilters($filters);
        
        // Get organizations for filter dropdown
        $organizations = $this->organizationModel->where('deleted_at', null)
                                                 ->where('is_active', 1)
                                                 ->findAll();

        $data = [
            'title' => 'User Management - PROMIS Admin',
            'page_title' => 'User Management',
            'users' => $users,
            'organizations' => $organizations,
            'filters' => $filters
        ];

        return view('admin/admin_users_list', $data);
    }

    /**
     * Show user creation step 1 form - GET request
     */
    public function createUserStep1()
    {
        $data = [
            'title' => 'Create User - Step 1 - PROMIS Admin',
            'page_title' => 'Create User - Account Details'
        ];

        return view('admin/admin_users_create_step1', $data);
    }

    /**
     * Process user creation step 1 - POST request
     */
    public function processUserStep1()
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token. Please try again.');
        }

        // Validation rules for step 1
        $rules = [
            'username' => 'required|min_length[3]|is_unique[users.username]',
            'email' => 'required|valid_email|is_unique[users.email]',
            'name' => 'required|min_length[2]',
            'phone' => 'permit_empty|min_length[10]',
            'department' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Store step 1 data in session
        $step1Data = [
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'name' => $this->request->getPost('name'),
            'phone' => $this->request->getPost('phone'),
            'department' => $this->request->getPost('department')
        ];

        session()->set('user_creation_step1', $step1Data);

        // Generate temporary password
        $tempPassword = $this->generateTempPassword();
        session()->set('user_temp_password', $tempPassword);

        return redirect()->to(base_url('admin/users/create/step2/temp'))
                        ->with('success', 'Account details saved. Please configure roles and permissions.');
    }

    /**
     * Show user creation step 2 form - GET request
     */
    public function createUserStep2($tempId = null)
    {
        // Check if step 1 data exists
        $step1Data = session()->get('user_creation_step1');
        if (!$step1Data) {
            return redirect()->to(base_url('admin/users/create'))
                           ->with('error', 'Please complete step 1 first.');
        }

        // Get organizations for assignment
        $organizations = $this->organizationModel->where('deleted_at', null)
                                                 ->where('is_active', 1)
                                                 ->findAll();

        $data = [
            'title' => 'Create User - Step 2 - PROMIS Admin',
            'page_title' => 'Create User - Roles & Permissions',
            'step1_data' => $step1Data,
            'organizations' => $organizations,
            'temp_password' => session()->get('user_temp_password')
        ];

        return view('admin/admin_users_create_step2', $data);
    }

    /**
     * Process user creation step 2 and complete user creation - POST request
     */
    public function processUserStep2($tempId = null)
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token. Please try again.');
        }

        // Check if step 1 data exists
        $step1Data = session()->get('user_creation_step1');
        if (!$step1Data) {
            return redirect()->to(base_url('admin/users/create'))
                           ->with('error', 'Session expired. Please start over.');
        }

        // Validation rules for step 2
        $rules = [
            'organization_id' => 'required|numeric',
            'role' => 'required|in_list[admin,moderator,editor,user]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Combine step 1 and step 2 data
        $userData = array_merge($step1Data, [
            'organization_id' => $this->request->getPost('organization_id'),
            'role' => $this->request->getPost('role'),
            'password_hash' => password_hash(session()->get('user_temp_password'), PASSWORD_ARGON2ID),
            'is_activated' => 1, // Auto-activate for admin-created users
            'created_by' => session()->get('admin_user_id'),
            'created_at' => date('Y-m-d H:i:s')
        ]);

        try {
            $userId = $this->userModel->insert($userData);
            
            if ($userId) {
                // Clear session data
                session()->remove(['user_creation_step1', 'user_temp_password']);
                
                // Log the action
                $this->logAuditEvent('create', session()->get('admin_user_id'), 'users', $userId, [
                    'description' => 'New user created: ' . $userData['username'],
                    'user_data' => $userData
                ]);

                return redirect()->to(base_url('admin/users'))
                               ->with('success', 'User created successfully! Username: ' . $userData['username'] . ', Temporary Password: ' . session()->get('user_temp_password'));
            } else {
                return redirect()->back()->withInput()->with('errors', $this->userModel->errors());
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Error creating user: ' . $e->getMessage());
        }
    }

    /**
     * Show password reset modal - GET request
     */
    public function showResetPasswordModal($userId)
    {
        $user = $this->userModel->find($userId);
        
        if (!$user) {
            return $this->response->setStatusCode(404)->setBody('User not found');
        }

        $data = [
            'user' => $user
        ];

        return view('admin/admin_users_reset_password_modal', $data);
    }

    /**
     * Process password reset - POST request
     */
    public function processPasswordReset($userId)
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token');
        }

        $user = $this->userModel->find($userId);
        
        if (!$user) {
            return redirect()->back()->with('error', 'User not found');
        }

        // Generate new temporary password
        $tempPassword = $this->generateTempPassword();
        $hashedPassword = password_hash($tempPassword, PASSWORD_ARGON2ID);

        // Update user password
        $updateData = [
            'password_hash' => $hashedPassword,
            'updated_by' => session()->get('admin_user_id'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($this->userModel->update($userId, $updateData)) {
            // Log the action
            $this->logAuditEvent('update', session()->get('admin_user_id'), 'users', $userId, [
                'description' => 'Password reset for user: ' . $user['username'],
                'action_type' => 'password_reset'
            ]);

            return redirect()->back()->with('success', 'Password reset successfully! New temporary password: ' . $tempPassword);
        } else {
            return redirect()->back()->with('error', 'Failed to reset password');
        }
    }

    /**
     * View active sessions - GET request
     */
    public function viewActiveSessions()
    {
        // This is a placeholder - in a real implementation, you'd query session storage
        $sessions = [
            [
                'user_id' => session()->get('admin_user_id'),
                'username' => session()->get('admin_username'),
                'ip_address' => $this->request->getIPAddress(),
                'user_agent' => $this->request->getUserAgent(),
                'login_time' => date('Y-m-d H:i:s', session()->get('admin_last_activity')),
                'session_id' => session()->session_id,
                'status' => 'active'
            ]
        ];

        $data = [
            'title' => 'Active Sessions - PROMIS Admin',
            'page_title' => 'Active User Sessions',
            'sessions' => $sessions
        ];

        return view('admin/admin_users_sessions', $data);
    }

    /**
     * Terminate session - POST request
     */
    public function terminateSession($sessionId)
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token');
        }

        // In a real implementation, you'd terminate the specific session
        // For now, we'll just show a success message
        return redirect()->back()->with('success', 'Session terminated successfully');
    }

    /**
     * Get users with filters applied
     */
    private function getUsersWithFilters($filters)
    {
        $builder = $this->userModel->select('users.*, organizations.name as org_name')
                                  ->join('organizations', 'organizations.id = users.organization_id', 'left')
                                  ->where('users.deleted_at', null);

        // Apply filters
        if (!empty($filters['role'])) {
            $builder->where('users.role', $filters['role']);
        }

        if (!empty($filters['status'])) {
            if ($filters['status'] === 'active') {
                $builder->where('users.is_activated', 1);
            } else {
                $builder->where('users.is_activated', 0);
            }
        }

        if (!empty($filters['organization'])) {
            $builder->where('users.organization_id', $filters['organization']);
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('users.name', $filters['search'])
                   ->orLike('users.username', $filters['search'])
                   ->orLike('users.email', $filters['search'])
                   ->groupEnd();
        }

        return $builder->orderBy('users.created_at', 'DESC')->findAll();
    }

    /**
     * Generate temporary password
     */
    private function generateTempPassword()
    {
        return str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * Log audit event
     */
    private function logAuditEvent($action, $userId, $tableName, $primaryKey, $data = [])
    {
        $auditData = [
            'table_name' => $tableName,
            'primary_key' => (string)$primaryKey,
            'action' => $action,
            'new_data' => json_encode($data),
            'user_id' => $userId,
            'username' => session()->get('admin_username'),
            'user_type' => 'admin_user',
            'user_full_name' => session()->get('admin_user_name'),
            'organization_id' => session()->get('admin_organization_id'),
            'organization_name' => session()->get('admin_organization_name'),
            'organization_type' => 'Organization',
            'portal' => 'admin',
            'module' => 'user_management',
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent(),
            'session_id' => session()->session_id,
            'request_url' => current_url(),
            'description' => $data['description'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $db = \Config\Database::connect();
        $db->table('audit_logs')->insert($auditData);
    }
}
